{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./dist"}, "include": ["backend/src/**/*", "frontend/src/**/*"], "exclude": ["node_modules", "dist", "build"]}