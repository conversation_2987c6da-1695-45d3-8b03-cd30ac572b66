#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Chat with DB Setup...\n');

// Check if all required files exist
const requiredFiles = [
  'package.json',
  'backend/package.json',
  'frontend/package.json',
  'backend/src/index.ts',
  'frontend/src/App.tsx',
  '.env'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

console.log('\n📁 Project Structure:');
console.log('chatWithDb/');
console.log('├── backend/');
console.log('│   ├── src/');
console.log('│   │   ├── routes/');
console.log('│   │   ├── services/');
console.log('│   │   └── types/');
console.log('│   └── package.json');
console.log('├── frontend/');
console.log('│   ├── src/');
console.log('│   │   ├── components/');
console.log('│   │   ├── services/');
console.log('│   │   └── types/');
console.log('│   └── package.json');
console.log('├── package.json');
console.log('└── .env');

console.log('\n🚀 Next Steps:');
console.log('1. Make sure MongoDB is running locally');
console.log('2. Make sure Ollama is installed and running with a model');
console.log('3. Run: npm run dev');
console.log('4. Open http://localhost:3000 in your browser');

console.log('\n📋 Prerequisites Check:');
console.log('- MongoDB: Run `mongosh` to test connection');
console.log('- Ollama: Run `ollama list` to see available models');

if (allFilesExist) {
  console.log('\n🎉 Setup appears to be complete!');
} else {
  console.log('\n⚠️  Some files are missing. Please check the setup.');
}
