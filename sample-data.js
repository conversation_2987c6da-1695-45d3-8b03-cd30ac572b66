#!/usr/bin/env node

const { MongoClient } = require('mongodb');

const sampleUsers = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    age: 28,
    city: "New York",
    createdAt: new Date('2024-01-15'),
    isActive: true
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    age: 34,
    city: "Los Angeles",
    createdAt: new Date('2024-02-20'),
    isActive: true
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    age: 45,
    city: "Chicago",
    createdAt: new Date('2024-01-10'),
    isActive: false
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    age: 29,
    city: "Houston",
    createdAt: new Date('2024-03-05'),
    isActive: true
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    age: 52,
    city: "Phoenix",
    createdAt: new Date('2024-02-28'),
    isActive: true
  }
];

async function insertSampleData() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    console.log('🔌 Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db('test');
    const collection = db.collection('users');
    
    // Clear existing data
    console.log('🗑️  Clearing existing users...');
    await collection.deleteMany({});
    
    // Insert sample data
    console.log('📝 Inserting sample users...');
    const result = await collection.insertMany(sampleUsers);
    
    console.log(`✅ Successfully inserted ${result.insertedCount} users`);
    
    // Show the data
    console.log('\n👥 Sample Users:');
    const users = await collection.find({}).toArray();
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.age}) - ${user.email} - ${user.city}`);
    });
    
    console.log('\n🎯 Try asking questions like:');
    console.log('- "How many users do we have?"');
    console.log('- "Show me users from New York"');
    console.log('- "What is the average age of users?"');
    console.log('- "Find users with Gmail addresses"');
    console.log('- "Show me inactive users"');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n💡 Make sure MongoDB is running:');
    console.log('   - Install MongoDB: https://www.mongodb.com/try/download/community');
    console.log('   - Start MongoDB: mongod');
    console.log('   - Test connection: mongosh');
  } finally {
    await client.close();
  }
}

if (require.main === module) {
  insertSampleData();
}

module.exports = { insertSampleData, sampleUsers };
