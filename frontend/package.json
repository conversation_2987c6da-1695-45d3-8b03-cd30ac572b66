{"name": "frontend", "version": "1.0.0", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.2"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/node": "^20.10.5", "typescript": "^5.3.3", "vite": "^5.0.8", "@vitejs/plugin-react": "^4.2.1"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}