import React from 'react';
import { ChatMessage } from '../types';

interface MessageListProps {
  messages: ChatMessage[];
}

export const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  return (
    <div className="message-list">
      {messages.length === 0 ? (
        <div className="welcome-message">
          <h3>Welcome to Chat with DB!</h3>
          <p>Ask questions about your MongoDB database in natural language.</p>
          <div className="example-questions">
            <p><strong>Try asking:</strong></p>
            <ul>
              <li>"How many users do we have?"</li>
              <li>"Show me users created this month"</li>
              <li>"What are the most common user ages?"</li>
              <li>"Find users with email containing 'gmail'"</li>
            </ul>
          </div>
        </div>
      ) : (
        messages.map((message) => (
          <div
            key={message.id}
            className={`message ${message.isUser ? 'user-message' : 'ai-message'}`}
          >
            <div className="message-content">
              <div className="message-text">{message.message}</div>
              <div className="message-timestamp">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
            
            {!message.isUser && message.query && (
              <details className="query-details">
                <summary>View MongoDB Query</summary>
                <pre className="query-code">
                  {JSON.stringify(message.query, null, 2)}
                </pre>
              </details>
            )}
            
            {!message.isUser && message.results && message.results.length > 0 && (
              <details className="results-details">
                <summary>View Raw Results ({message.results.length} items)</summary>
                <pre className="results-code">
                  {JSON.stringify(message.results.slice(0, 3), null, 2)}
                  {message.results.length > 3 && '\n... and more'}
                </pre>
              </details>
            )}
          </div>
        ))
      )}
    </div>
  );
};
