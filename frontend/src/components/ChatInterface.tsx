import React, { useState, useEffect, useRef } from 'react';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { apiService } from '../services/apiService';
import { ChatMessage } from '../types';

export const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    ollama: string;
    mongodb: string;
  } | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Check connection status on component mount
    checkConnectionStatus();
  }, []);

  const checkConnectionStatus = async () => {
    try {
      const health = await apiService.getHealth();
      setConnectionStatus(health.services);
    } catch (error) {
      console.error('Failed to check connection status:', error);
      setConnectionStatus({ ollama: 'disconnected', mongodb: 'disconnected' });
    }
  };

  const handleSendMessage = async (messageText: string) => {
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: messageText,
      timestamp: new Date(),
      isUser: true,
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Add a temporary "thinking" message
    const thinkingMessage: ChatMessage = {
      id: 'thinking-' + Date.now().toString(),
      message: 'AI is processing your question... This may take up to 2 minutes for complex queries.',
      timestamp: new Date(),
      isUser: false,
    };
    setMessages(prev => [...prev, thinkingMessage]);

    try {
      const response = await apiService.sendMessage({
        message: messageText,
        database: 'test', // Default database
        collection: 'users', // Default collection
      });

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: response.response,
        timestamp: new Date(),
        isUser: false,
        query: response.query,
        results: response.results,
      };

      // Remove thinking message and add AI response
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('thinking-')).concat(aiMessage));
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        isUser: false,
      };

      // Remove thinking message and add error message
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('thinking-')).concat(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="chat-interface">
      <div className="chat-header">
        <h1>Chat with DB</h1>
        <div className="connection-status">
          <div className={`status-indicator ${connectionStatus?.ollama === 'connected' ? 'connected' : 'disconnected'}`}>
            Ollama: {connectionStatus?.ollama || 'checking...'}
          </div>
          <div className={`status-indicator ${connectionStatus?.mongodb === 'connected' ? 'connected' : 'disconnected'}`}>
            MongoDB: {connectionStatus?.mongodb || 'checking...'}
          </div>
          <button onClick={checkConnectionStatus} className="refresh-status">
            ↻
          </button>
        </div>
      </div>

      <div className="chat-messages">
        <MessageList messages={messages} />
        <div ref={messagesEndRef} />
      </div>

      <div className="chat-input">
        <MessageInput onSendMessage={handleSendMessage} isLoading={isLoading} />
      </div>
    </div>
  );
};
