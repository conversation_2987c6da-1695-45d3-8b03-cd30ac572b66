* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

.App {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-interface {
  height: 100vh;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.chat-header {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chat-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

.connection-status {
  display: flex;
  gap: 1rem;
  align-items: center;
  font-size: 0.875rem;
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 500;
}

.status-indicator.connected {
  background-color: rgba(34, 197, 94, 0.2);
  color: #16a34a;
}

.status-indicator.disconnected {
  background-color: rgba(239, 68, 68, 0.2);
  color: #dc2626;
}

.refresh-status {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.refresh-status:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: #fafafa;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.welcome-message {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.welcome-message h3 {
  color: #333;
  margin-bottom: 1rem;
}

.example-questions {
  margin-top: 2rem;
  text-align: left;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.example-questions ul {
  list-style: none;
  padding-left: 0;
}

.example-questions li {
  padding: 0.5rem 0;
  font-style: italic;
  color: #888;
}

.message {
  display: flex;
  margin-bottom: 1rem;
}

.user-message {
  justify-content: flex-end;
}

.ai-message {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 1rem 1.5rem;
  border-radius: 18px;
  position: relative;
}

.user-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.ai-message .message-content {
  background: white;
  color: #333;
  border: 1px solid #e5e5e5;
  border-bottom-left-radius: 4px;
}

.message-text {
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-timestamp {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-top: 0.5rem;
}

.query-details, .results-details {
  margin-top: 1rem;
  max-width: 70%;
}

.ai-message .query-details,
.ai-message .results-details {
  margin-left: 0;
}

.query-details summary,
.results-details summary {
  cursor: pointer;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 0.875rem;
  color: #666;
}

.query-details summary:hover,
.results-details summary:hover {
  background-color: #e9ecef;
}

.query-code, .results-code {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  overflow-x: auto;
  margin-top: 0.5rem;
  color: #333;
}

.chat-input {
  padding: 1rem 2rem 2rem;
  background: white;
  border-top: 1px solid #e5e5e5;
}

.message-input-form {
  width: 100%;
}

.input-container {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.message-textarea {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  min-height: 60px;
  max-height: 120px;
  outline: none;
  transition: border-color 0.2s;
}

.message-textarea:focus {
  border-color: #667eea;
}

.message-textarea:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.send-button {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  min-width: 100px;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .connection-status {
    font-size: 0.75rem;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .chat-input {
    padding: 1rem;
  }
  
  .input-container {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .send-button {
    align-self: flex-end;
    min-width: 80px;
  }
}
