{"name": "backend", "version": "1.0.0", "description": "Backend API for chat with MongoDB", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "mongodb": "^6.3.0", "langchain": "^0.1.0", "@langchain/community": "^0.0.20", "@langchain/core": "^0.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.5", "typescript": "^5.3.3", "tsx": "^4.6.2"}}