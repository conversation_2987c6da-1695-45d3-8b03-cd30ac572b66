export interface ChatMessage {
  id: string;
  message: string;
  timestamp: Date;
  isUser: boolean;
}

export interface ChatRequest {
  message: string;
  database?: string;
  collection?: string;
}

export interface ChatResponse {
  response: string;
  query?: string;
  results?: any[];
  error?: string;
}

export interface DatabaseSchema {
  database: string;
  collection: string;
  sampleDocument?: any;
  fields?: string[];
  indexes?: any[];
}
