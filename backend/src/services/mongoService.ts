import { MongoClient, Db, Collection } from 'mongodb';

export class MongoService {
  private client: MongoClient;
  private db: Db | null = null;
  private isConnected = false;

  constructor(connectionString: string = 'mongodb://localhost:27017') {
    this.client = new MongoClient(connectionString);
  }

  async connect(databaseName: string = 'test'): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.client.connect();
        this.isConnected = true;
        console.log('Connected to MongoDB');
      }
      this.db = this.client.db(databaseName);
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await this.client.close();
      this.isConnected = false;
      console.log('Disconnected from MongoDB');
    }
  }

  getDatabase(): Db {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db;
  }

  getCollection(collectionName: string): Collection {
    return this.getDatabase().collection(collectionName);
  }

  async getCollectionSchema(collectionName: string): Promise<any> {
    try {
      const collection = this.getCollection(collectionName);

      // Check if collection exists first
      const collections = await this.listCollections();
      const collectionExists = collections.includes(collectionName);

      if (!collectionExists) {
        console.log(`Collection ${collectionName} does not exist. Returning empty schema.`);
        return {
          collection: collectionName,
          sampleDocument: null,
          documentCount: 0,
          indexes: [],
          fields: [],
          exists: false
        };
      }

      // Get a sample document to understand the schema
      const sampleDoc = await collection.findOne({});

      let stats;
      let indexes = [];

      try {
        // Get collection stats (might fail if collection is empty)
        stats = await this.getDatabase().command({ collStats: collectionName });
      } catch (statsError) {
        console.log(`Could not get stats for ${collectionName}, using defaults`);
        stats = { count: 0 };
      }

      try {
        // Get indexes (might fail if collection doesn't exist)
        indexes = await collection.indexes();
      } catch (indexError) {
        console.log(`Could not get indexes for ${collectionName}, using empty array`);
        indexes = [];
      }

      return {
        collection: collectionName,
        sampleDocument: sampleDoc,
        documentCount: stats.count || 0,
        indexes: indexes,
        fields: sampleDoc ? Object.keys(sampleDoc) : [],
        exists: true
      };
    } catch (error) {
      console.error(`Error getting schema for collection ${collectionName}:`, error);
      // Return a safe default instead of throwing
      return {
        collection: collectionName,
        sampleDocument: null,
        documentCount: 0,
        indexes: [],
        fields: [],
        exists: false,
        error: error.message
      };
    }
  }

  async executeQuery(collectionName: string, query: any, options: any = {}): Promise<any[]> {
    try {
      const collection = this.getCollection(collectionName);

      // Handle different types of queries
      if (query.aggregate) {
        return await collection.aggregate(query.aggregate, options).toArray();
      } else if (query.find) {
        const cursor = collection.find(query.find, options);
        if (query.limit) cursor.limit(query.limit);
        if (query.sort) cursor.sort(query.sort);
        return await cursor.toArray();
      } else {
        // Default to find with the query as filter
        return await collection.find(query, options).limit(10).toArray();
      }
    } catch (error) {
      console.error('Error executing query:', error);
      throw error;
    }
  }

  async listCollections(): Promise<string[]> {
    try {
      const collections = await this.getDatabase().listCollections().toArray();
      return collections.map(col => col.name);
    } catch (error) {
      console.error('Error listing collections:', error);
      throw error;
    }
  }
}
