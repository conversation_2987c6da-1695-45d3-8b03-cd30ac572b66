import { Router, Request, Response } from 'express';
import { LangChainService } from '../services/langchainService';
import { MongoService } from '../services/mongoService';
import { ChatRequest, ChatResponse } from '../types';

const router = Router();

// Initialize services
const mongoService = new MongoService(process.env.MONGODB_URI || 'mongodb://localhost:27017');
console.log('>>>>>>', process.env.OLLAMA_MODEL);
const langchainService = new LangChainService(mongoService, process.env.OLLAMA_MODEL || 'llama2');

router.post('/chat', async (req: Request, res: Response) => {
  try {
    const { message, database = 'test', collection = 'users' }: ChatRequest = req.body;

    if (!message) {
      return res.status(400).json({
        error: 'Message is required'
      } as ChatResponse);
    }

    console.log(`Processing question: "${message}" for ${database}.${collection}`);

    // Process the user's question
    const result = await langchainService.processUserQuestion(message, collection, database);

    const response: ChatResponse = {
      response: result.response,
      query: result.query,
      results: result.results
    };

    res.json(response);

  } catch (error) {
    console.error('Chat endpoint error:', error);

    const errorResponse: ChatResponse = {
      response: 'I apologize, but I encountered an error while processing your request. Please try again.',
      error: error instanceof Error ? error.message : 'Unknown error'
    };

    res.status(500).json(errorResponse);
  }
});

// Health check endpoint
router.get('/health', async (req: Request, res: Response) => {
  try {
    // Test Ollama connection
    const ollamaStatus = await langchainService.testConnection();

    // Test MongoDB connection
    let mongoStatus = false;
    try {
      await mongoService.connect();
      const collections = await mongoService.listCollections();
      mongoStatus = true;
    } catch (error) {
      console.error('MongoDB health check failed:', error);
    }

    res.json({
      status: 'ok',
      services: {
        ollama: ollamaStatus ? 'connected' : 'disconnected',
        mongodb: mongoStatus ? 'connected' : 'disconnected'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get available collections
router.get('/collections', async (req: Request, res: Response) => {
  try {
    const database = req.query.database as string || 'test';
    await mongoService.connect(database);
    const collections = await mongoService.listCollections();

    res.json({
      database,
      collections
    });
  } catch (error) {
    console.error('Collections endpoint error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch collections'
    });
  }
});

export default router;
